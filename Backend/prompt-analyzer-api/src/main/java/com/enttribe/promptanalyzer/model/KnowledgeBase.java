/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.model;

import com.enttribe.promptanalyzer.util.ByteCodeMapConverter;
import jakarta.persistence.*;
import java.util.Map;

import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import lombok.Getter;
import lombok.Setter;
import org.hibernate.validator.constraints.Range;

/**
 * Entity representing a knowledge base entry. Stores various types of knowledge sources such as
 * SQL, API, documents, and code.
 *
 * <AUTHOR>
 * @version 1.0
 */
@Entity
@Table(name = "KNOWLEDGE_BASE")
@Getter
@Setter
public class KnowledgeBase extends BaseEntityGlobal {

  /** Unique ID of the knowledge base entry. */
  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ID", columnDefinition = "INT")
  private Integer id;

  /** Unique ID of the knowledge base entry. */
  @Column(name = "NANO_ID", length = 50)
  private String nanoId;

  /** Name of the knowledge base entry. */
  @Column(name = "NAME")
  private String name;

  /** Description of the knowledge base entry. */
  @Column(name = "DESCRIPTION", length = 1200)
  private String description;

  /** Type of the knowledge source (e.g., SQL, API, DOCUMENT, CODE). */
  @Column(
      name = "TYPE",
      nullable = false,
      columnDefinition = "ENUM('SQL', 'API', 'DOCUMENT', 'CODE', 'WEBSITE')")
  private String type;

  /** Class name associated with the knowledge base entry. */
  @Column(name = "CLASS_NAME")
  private String className;

  /** Url of website for website type knowledge base. */
  @Column(name = "WEBSITE_URL", length = 500)
  private String webSiteUrl;

  /** File name associated with the knowledge base entry. */
  @Column(name = "FILE_NAME")
  private String fileName;

  /** Database name if the type is SQL. */
  @Column(name = "DB_NAME")
  private String dbName;

  /** Database username if the type is SQL. */
  @Column(name = "DB_USERNAME")
  private String dbUsername;

  /** Database password if the type is SQL. */
  @Column(name = "DB_PASSWORD")
  private String dbPassword;

  /** API endpoint if the type is API. */
  @Column(name = "API_ENDPOINT")
  private String apiEndpoint;

  /** API type if the type is API. */
  @Column(name = "API_TYPE")
  private String apiType;

  /** API authentication type if the type is API. */
  @Column(name = "API_AUTH_TYPE")
  private String
      apiAuthType; // Also used for temporarily storing the taskId of website type knowledge base.

  /** Status of website crawling task. */
  @Column(name = "WEBSITE_TASK_STATUS", length = 20)
  private String websiteTaskStatus;

  /** ID of website crawling task. */
  @Column(name = "WEBSITE_TASK_ID", length = 70)
  private String websiteTaskId;

  /** Error message from website crawling. */
  @Column(name = "WEBSITE_TASK_ERROR", length = 1500)
  private String websiteTaskError;

  /** Name of the collection. */
  @Column(name = "COLLECTION_NAME", length = 40)
  private String collectionName;

  /** API authentication value if the type is API. */
  @Column(name = "API_AUTH_VALUE")
  private String apiAuthValue;

  /** Indicates if the entry is marked as deleted. */
  @Column(name = "DELETED")
  private Boolean deleted = false;

  /** Document type if the type is DOCUMENT. */
  @Column(name = "DOC_TYPE")
  private String docType;

  /** Document ID if the type is DOCUMENT. */
  @Column(name = "DOC_ID")
  private String docId;

  /** Document ID if the type is DOCUMENT. */
  @Column(name = "FILTER")
  private String filter;

  /** Metadata of the document if the type is DOCUMENT. */
  @Column(name = "DOC_META_DATA", columnDefinition = "LONGTEXT")
  private String docMetaData;

  /** Metadata of the vector if applicable. */
  @Column(name = "VECTOR_META_DATA", columnDefinition = "LONGTEXT")
  private String vectorMetaData;

  /** Indicates if the entry is used as context. */
  @Column(name = "IS_CONTEXT")
  private Boolean isContext;

  /** Top K results to consider. */
  @Column(name = "TOP_K")
  private Integer topK;

  /** Similarity threshold for matching. */
  @Column(name = "SIMILARITY_THRESHOLD")
  private Double similarityThreshold;

  /** Source code if the type is SOURCE_CODE. */
  @Lob
  @Column(name = "SOURCE_CODE", columnDefinition = "TEXT")
  private String sourceCode;

  /** Byte code map if the type is SOURCE_CODE. */
  @Lob
  @Convert(converter = ByteCodeMapConverter.class)
  @Column(name = "BYTE_CODE_MAP", columnDefinition = "LONGTEXT")
  private Map<String, byte[]> byteCodeMap;

  /** Tag information. */
  @Column(name = "TAG")
  private String tag;

  /** Associated tables. */
  @Column(name = "TABLES")
  private String tables;

  /** Integration type. */
  @Column(name = "INTEGRATION")
  private String integration;

  @Column(name = "RETURN_DIRECT")
  private Boolean returnDirect = false;

  @Column(name = "S3_FILE_NAMES", columnDefinition = "Text")
  private String s3FileNames;
}
