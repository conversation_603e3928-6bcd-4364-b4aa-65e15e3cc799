/*
 * Copyright (c) 2024 Vwaves Technologies Private Limited
 *
 * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.
 * Product: Aspose.Total for Java
 * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations
 * Order ID: 250610115636
 *
 * This source code is part of a proprietary software system and may not be
 * distributed, modified, or reused without explicit written permission from
 * Vwaves Technologies Private Limited.
 *
 * Contact: <EMAIL>
 */
package com.enttribe.promptanalyzer.config.redis;

import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStore;
import com.enttribe.promptanalyzer.vectorstore.Redis8VectorStoreConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ai.embedding.BatchingStrategy;
import org.springframework.ai.embedding.EmbeddingModel;
import org.springframework.ai.embedding.TokenCountBatchingStrategy;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import redis.clients.jedis.JedisPoolConfig;
import redis.clients.jedis.DefaultJedisClientConfig;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.JedisClientConfig;
import redis.clients.jedis.JedisSentinelPool;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.security.cert.CertificateException;
import java.time.Duration;
import javax.net.ssl.SSLSocketFactory;
import java.security.KeyStore;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.io.FileInputStream;
import java.util.Arrays;
import java.util.Set;
import java.util.stream.Collectors;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManagerFactory;

/**
 * Configuration for Redis 8 Vector Store using native vector sets.
 * This configuration provides Redis 8 vector store beans that use the new
 * VADD, VSIM, VDEL commands instead of Redis Stack's FT. commands.
 * Enable this configuration by setting:
 * vector.store.type=redis8
 * 
 * <AUTHOR>
 * @version 1.0
 */
@Configuration
@ConditionalOnProperty(name = "vector.store.type", havingValue = "redis8")
public class Redis8VectorConfig {

    private static final Logger log = LoggerFactory.getLogger(Redis8VectorConfig.class);
    
    private static final int TIME_BETWEEN_EVICTION_RUNS_MILLIS = 30000;
    private static final int MIN_EVICTABLE_IDLE_DURATION_MILLIS = 60000;

    @Value("${spring.ai.vectorstore.redis.index-name}")
    private String indexName;

    @Value("${spring.ai.vectorstore.redis.prefix}")
    private String redisPrefix;

    @Value("${vector.store.redis8.quantization}")
    private String quantizationType;

    @Value("${vector.store.redis8.dimensionality-reduction.enabled}")
    private boolean dimensionalityReductionEnabled;

    @Value("${vector.store.redis8.dimensionality-reduction.target-dimensions}")
    private int targetDimensions;

    @Value("${vector.store.redis8.initialize-schema}")
    private boolean initializeSchema;

    @Value("${vector.store.redis8.masterName}")
    private String redisMasterName;

    @Value("${vector.store.redis8.sentinels}")
    private String redisSentinels;

    @Value("${spring.data.redis.username}")
    private String redisUsername;

    @Value("${spring.data.redis8.password}")
    private String redisPassword;

    @Value("${spring.data.redis.ssl.enable}")
    private boolean sslEnable;

    @Value("${vector.redis8.trustStorePath:}")
    private String trustStorePath;

    @Value("${spring.data.redis.timeout.connection}")
    private int connectionTimeout;

    @Value("${spring.data.redis.timeout.socket}")
    private int socketTimeout;

    @Value("${spring.data.redis.pool.max-total}")
    private int maxTotal;

    @Value("${spring.data.redis.pool.max-idle}")
    private int maxIdle;

    @Value("${spring.data.redis.pool.min-idle}")
    private int minIdle;

    /**
     * JedisPool bean for Redis 8 vector operations
     */
    @Bean
    public JedisSentinelPool jedisSentinelPool() throws Exception {
        log.info("Creating JedisSentinelPool for Redis Sentinel with master: {}, sentinels: {}, ssl: {}",
                redisMasterName, redisSentinels, sslEnable);

        // Pool config
        JedisPoolConfig poolConfig = new JedisPoolConfig();
        poolConfig.setMaxTotal(maxTotal);
        poolConfig.setMaxIdle(maxIdle);
        poolConfig.setMinIdle(minIdle);
        poolConfig.setTestOnBorrow(true);
        poolConfig.setTestWhileIdle(true);
        poolConfig.setTimeBetweenEvictionRuns(Duration.ofMillis(TIME_BETWEEN_EVICTION_RUNS_MILLIS));
        poolConfig.setMinEvictableIdleDuration(Duration.ofMillis(MIN_EVICTABLE_IDLE_DURATION_MILLIS));
        poolConfig.setNumTestsPerEvictionRun(-1);

        // SSL setup
        SSLSocketFactory sslSocketFactory = null;
        if (sslEnable && trustStorePath != null && !trustStorePath.isEmpty()) {
            log.info("Setting up SSL for Redis Sentinel connection with trust store: {}", trustStorePath);
            sslSocketFactory = createSSLSocketFactory(trustStorePath);
        }

        // Master client config
        DefaultJedisClientConfig.Builder masterConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(connectionTimeout)
                .socketTimeoutMillis(socketTimeout)
                .ssl(sslEnable)
                .sslSocketFactory(sslSocketFactory)
                .database(0);

        if (redisUsername != null && !redisUsername.isEmpty()) {
            masterConfigBuilder.user(redisUsername);
        }
        if (redisPassword != null && !redisPassword.isEmpty()) {
            masterConfigBuilder.password(redisPassword);
        }

        // Sentinel client config (optional user/pass; same SSL factory)
        DefaultJedisClientConfig.Builder sentinelConfigBuilder = DefaultJedisClientConfig.builder()
                .connectionTimeoutMillis(connectionTimeout)
                .socketTimeoutMillis(socketTimeout)
                .ssl(sslEnable)
                .sslSocketFactory(sslSocketFactory);

        if (redisUsername != null && !redisUsername.isEmpty()) {
            sentinelConfigBuilder.user(redisUsername);
        }
        if (redisPassword != null && !redisPassword.isEmpty()) {
            sentinelConfigBuilder.password(redisPassword);
        }

        JedisClientConfig masterClientConfig = masterConfigBuilder.build();
        JedisClientConfig sentinelClientConfig = sentinelConfigBuilder.build();

        // Parse sentinels
        Set<HostAndPort> sentinelSet = Arrays.stream(redisSentinels.split(","))
                .map(String::trim)
                .map(HostAndPort::from)
                .collect(Collectors.toSet());

        return new JedisSentinelPool(redisMasterName, sentinelSet, poolConfig, masterClientConfig, sentinelClientConfig);
    }

    /**
     * Create SSL socket factory for Redis connection
     */
    private SSLSocketFactory createSSLSocketFactory(String caCertPath)
            throws CertificateException, KeyStoreException, IOException, NoSuchAlgorithmException, KeyManagementException {
        CertificateFactory cf = CertificateFactory.getInstance("X.509");

        X509Certificate caCert;
        try (FileInputStream fis = new FileInputStream(caCertPath)) {
            caCert = (X509Certificate) cf.generateCertificate(fis);
        }

        KeyStore keyStore = KeyStore.getInstance(KeyStore.getDefaultType());
        keyStore.load(null, null);
        keyStore.setCertificateEntry("redis8-ca", caCert);

        TrustManagerFactory tmf = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm());
        tmf.init(keyStore);

        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, tmf.getTrustManagers(), null);

        return sslContext.getSocketFactory();
    }

    /**
     * Primary vector store bean using Redis 8 vector sets
     */
    @Bean
    @Primary
    public VectorStore vectorStore(JedisSentinelPool jedisPool,
                                       EmbeddingModel embeddingModel,
                                       BatchingStrategy batchingStrategy) {
        log.info("Creating Redis 8 vector store with vector set: {}, prefix: {}", indexName, redisPrefix);

        Redis8VectorStoreConfig config = Redis8VectorStoreConfig.builder()
            .vectorSetName(indexName)
            .keyPrefix(redisPrefix)
            .quantizationType(parseQuantizationType(quantizationType))
            .initializeSchema(initializeSchema)
            .targetDimensions(dimensionalityReductionEnabled ? targetDimensions : -1)
            .build();

        Redis8VectorStore vectorStore = Redis8VectorStore.builder(jedisPool, embeddingModel)
            .batchingStrategy(batchingStrategy)
            .config(config)
            .build();

        log.info("Redis 8 vector store created successfully with config: {}", config);
        return vectorStore;
    }

    @Bean
    @ConditionalOnMissingBean(BatchingStrategy.class)
    BatchingStrategy batchingStrategy() {
        return new TokenCountBatchingStrategy();
    }

    /**
     * Parse quantization type from string configuration
     * Supports both Redis native values (Q8, BIN, NOQUANT) and friendly names
     */
    private Redis8VectorStoreConfig.QuantizationType parseQuantizationType(String type) {
        if (type == null) {
            return Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
        }

        return switch (type.toUpperCase()) {
            // Redis native values
            case "Q8", "8BIT", "QUANTIZE_8BIT" -> Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
            case "BIN", "BINARY" -> Redis8VectorStoreConfig.QuantizationType.BINARY;
            case "NOQUANT", "NONE", "FULL" -> Redis8VectorStoreConfig.QuantizationType.NONE;
            default -> {
                log.warn("Unknown quantization type: {}, defaulting to Q8 (8-bit)", type);
                yield Redis8VectorStoreConfig.QuantizationType.QUANTIZE_8BIT;
            }
        };
    }
}
