/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.util;

import com.enttribe.promptanalyzer.exception.BusinessException;
import jakarta.persistence.*;
import java.lang.reflect.*;
import java.util.*;

/**
 * Utility class for cloning JPA entities with proper handling of different relationship types.
 *
 * <p>This class provides deep cloning capabilities for JPA entities while respecting
 * different relationship annotations:</p>
 * <ul>
 *   <li><strong>OneToMany</strong> - Deep clones the collection and its elements</li>
 *   <li><strong>ManyToMany</strong> - Shallow copies the collection</li>
 *   <li><strong>ManyToOne/OneToOne</strong> - Shallow copies the reference</li>
 *   <li><strong>Scalar fields</strong> - Direct value copy</li>
 * </ul>
 *
 * <p>The cloning process:</p>
 * <ul>
 *   <li>Skips static and final fields</li>
 *   <li>Skips fields annotated with @Id</li>
 *   <li>Handles null values gracefully</li>
 *   <li>Supports recursive deep cloning for OneToMany relationships</li>
 * </ul>
 *
 * <AUTHOR>
 * @version 1.0
 */
public class EntityCloner {

  private EntityCloner() {
    // Private constructor to hide the implicit public one
  }

  /**
   * Clones a JPA entity with proper handling of different relationship types.
   *
   * @param <T> the type of the entity to clone
   * @param source the entity to clone, can be null
   * @return a cloned entity with the same type as the source, or null if source is null
   * @throws RuntimeException if cloning fails due to reflection or instantiation issues
   */
  public static <T> T cloneEntity(T source) {
    if (source == null) {
      return null;
    }

    try {
      Class<?> clazz = source.getClass();
      @SuppressWarnings("unchecked")
      T clone = (T) clazz.getDeclaredConstructor().newInstance();

      for (Field field : getAllFields(clazz)) {
        int modifiers = field.getModifiers();

        // Skip static, final, or @Id fields
        if (Modifier.isStatic(modifiers) || Modifier.isFinal(modifiers) || field.isAnnotationPresent(Id.class)) {
          continue;
        }

        field.setAccessible(true);
        Object value = field.get(source);

        if (value == null) {
          field.set(clone, null);
        } else if (field.isAnnotationPresent(OneToMany.class)) {
          Collection<?> collection = (Collection<?>) value;
          Collection<Object> clonedCollection = createEmptyCollection(field.getType());
          for (Object item : collection) {
            clonedCollection.add(cloneEntity(item)); // recursive deep clone
          }
          field.set(clone, clonedCollection);
        } else if (field.isAnnotationPresent(ManyToMany.class)) {
          Collection<?> collection = (Collection<?>) value;
          field.set(clone, new HashSet<>(collection)); // shallow copy
        } else if (isEntity(value.getClass())) {
          field.set(clone, value); // shallow copy for OneToOne or ManyToOne
        } else {
          field.set(clone, value); // scalar field
        }
      }

      return clone;
    } catch (Exception e) {
      throw new BusinessException("Failed to clone entity", e);
    }
  }


  /**
   * Checks if a class is a JPA entity by looking for the @Entity annotation.
   *
   * @param clazz the class to check
   * @return true if the class is annotated with @Entity, false otherwise
   */
  private static boolean isEntity(Class<?> clazz) {
    return clazz.isAnnotationPresent(Entity.class);
  }

  /**
   * Creates an empty collection of the specified type.
   *
   * @param collectionType the type of collection to create
   * @return an empty collection of the specified type
   * @throws UnsupportedOperationException if the collection type is not supported
   */
  private static Collection<Object> createEmptyCollection(Class<?> collectionType) {
    if (collectionType.isAssignableFrom(Set.class)) {
      return new HashSet<>();
    } else if (collectionType.isAssignableFrom(List.class)) {
      return new ArrayList<>();
    } else if (!collectionType.isInterface()) {
      try {
        return (Collection<Object>) collectionType.getDeclaredConstructor().newInstance();
      } catch (Exception ignored) {
      }
    }
    throw new UnsupportedOperationException("Unsupported collection type: " + collectionType);
  }

  /**
   * Gets all fields from a class and its superclasses, excluding Object.class.
   *
   * @param clazz the class to get fields from
   * @return a list of all fields from the class and its superclasses
   */
  private static List<Field> getAllFields(Class<?> clazz) {
    List<Field> fields = new ArrayList<>();
    while (clazz != null && clazz != Object.class) {
      fields.addAll(List.of(clazz.getDeclaredFields()));
      clazz = clazz.getSuperclass();
    }
    return fields;
  }
}
