/*

           * Copyright (c) 2024 Vwaves Technologies Private Limited

           *

           * Licensed to Vwaves Technologies Private Limited under OEM Developer License by Aspose.

           * Product: Aspose.Total for Java

           * License Type: Developer OEM — 1 Developer and Unlimited Deployment Locations

           * Order ID: 250610115636

           *

           * This source code is part of a proprietary software system and may not be

           * distributed, modified, or reused without explicit written permission from

           * Vwaves Technologies Private Limited.

           *

           * Contact: <EMAIL>

           */
package com.enttribe.promptanalyzer.service.impl;

import com.enttribe.promptanalyzer.ai.dto.ChatCompletionRequestDto;
import com.enttribe.promptanalyzer.ai.dto.MessageDto;
import com.enttribe.promptanalyzer.constants.PromptConstants;
import com.enttribe.promptanalyzer.dao.PromptDao;
import com.enttribe.promptanalyzer.dto.ConversationPromptFormat;
import com.enttribe.promptanalyzer.exception.BusinessException;
import com.enttribe.promptanalyzer.exception.GuardException;
import com.enttribe.promptanalyzer.exception.ResourceNotFoundException;
import com.enttribe.promptanalyzer.manager.InferenceManager;
import com.enttribe.promptanalyzer.model.Message;
import com.enttribe.promptanalyzer.model.Prompt;
import com.enttribe.promptanalyzer.service.LlmApiService;
import com.enttribe.promptanalyzer.util.ChatModelUtils;
import com.enttribe.promptanalyzer.util.CustomPromptTemplate;
import com.enttribe.promptanalyzer.util.GuardUtils;
import com.enttribe.promptanalyzer.util.JsonUtils;
import com.enttribe.promptanalyzer.util.TemplateUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.messages.SystemMessage;
import org.springframework.ai.chat.model.ChatModel;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.ChatOptions;
import org.springframework.ai.converter.BeanOutputConverter;
import org.springframework.ai.converter.MapOutputConverter;
import org.springframework.ai.openai.OpenAiChatModel;
import org.springframework.ai.openai.OpenAiChatOptions;
import org.springframework.ai.openai.api.OpenAiApi;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletion;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionMessage;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionMessage.Role;
import org.springframework.ai.openai.api.OpenAiApi.ChatCompletionRequest;
import org.springframework.ai.openai.api.ResponseFormat;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * Implementation of the {@link LlmApiService} interface. This service manages interactions with
 * Large Language Model (LLM) APIs, providing functionality for chat completions and prompt
 * executions. It supports multiple LLM providers and handles message formatting, template
 * processing, and response management. The service includes capabilities for: - Chat completion
 * requests with various LLM providers - Prompt execution with variable substitution - Message
 * format standardization - Template variable processing
 *
 * <AUTHOR>
 * @version 1.0
 */
@Service
@Slf4j
public class LlmApiServiceImpl implements LlmApiService {

  private static final String SYSTEM = "system";
  public static final double TEMPERATURE = 0.3;

  private final InferenceManager inferenceManager;
  private final PromptDao promptRepository;
  private final PromptDao promptDao;
  private static final String FORMAT = "format";
  private static final String MODEL_LLAMA_3_3_70B_VERSATILE = "llama-3.3-70b-versatile";
  private static final String PROMPT_NOT_FOUND = "Error while executing prompt: {}";
  private static final String GENERATE_TEST_CASE_ERROR = "Error in @method generateTestCase : {}";

  public LlmApiServiceImpl(
      InferenceManager inferenceManager, PromptDao promptRepository, PromptDao promptDao) {
    this.inferenceManager = inferenceManager;
    this.promptRepository = promptRepository;
    this.promptDao = promptDao;
  }

  @Value("classpath:/template/fresh_system_prompt.st")
  private Resource freshSystemPrompt;

  @Value("classpath:/template/fresh_user_prompt.st")
  private Resource freshUserPrompt;

  @Value("classpath:/template/improve_system_prompt.st")
  private Resource improveSystemPrompt;

  @Value("classpath:/template/improve_user_prompt.st")
  private Resource improveUserPrompt;

  @Value("classpath:/template/system/generate_test_case.st")
  private Resource generateTestCaseSystem;

  @Value("classpath:/template/user/generate_test_case.st")
  private Resource generateTestCaseUser;

  @Override
  public Object chatCompletion(ChatCompletionRequestDto requestDto) {
    log.debug("Inside @method chatCompletion");
    try {
      List<MessageDto> messages = requestDto.getMessages();
      ResponseFormat responseFormat = null;
      if (requestDto.getJsonMode() != null && requestDto.getJsonMode()) {
        responseFormat = ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build();
        log.debug("JSON mode is enabled for chat completion");
      }
      List<ChatCompletionMessage> chatCompletionMessagesList =
          getChatCompletionMessagesList(messages);
      ChatCompletionRequest chatCompletionRequest =
          new ChatCompletionRequest(
              chatCompletionMessagesList,
              requestDto.getModel(),
              requestDto.getTemperature(),
              requestDto.getTopP(),
              requestDto.getMaxTokens(),
              responseFormat,
              requestDto.getReasoningEffort());

      String inference = requestDto.getProvider();
      inference = inference == null ? "groq" : inference;
      OpenAiApi openAiApi = inferenceManager.getOpenAiApiByProvider(inference);
      ResponseEntity<ChatCompletion> response =
          openAiApi.chatCompletionEntity(chatCompletionRequest);
      return Objects.requireNonNull(response.getBody()).choices().get(0).message();
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error while executing chat completion: {}", e.getMessage(), e);
      throw new BusinessException("Invalid request parameters: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error while executing chat completion: {}", e.getMessage(), e);
      throw new BusinessException(e.getMessage());
    }
  }

  @Override
  public Object executePrompt(String promptId, Map<String, Object> variableMap, Boolean format) {
    log.debug("Inside @method executePrompt with promptId: {}", promptId);
    try {
      Prompt prompt = promptRepository.getPromptByPromptId(promptId);
      Assert.notNull(prompt, "prompt is not found with id " + promptId);
      List<MessageDto> messageDtos = prepareMessageDtos(prompt, variableMap);

      if (Boolean.TRUE.equals(format)) {
        applyDefaultJsonFormat(messageDtos);
      }

      ResponseFormat responseFormat = getResponseFormat(prompt);
      List<ChatCompletionMessage> chatCompletionMessagesList =
          getChatCompletionMessagesList(messageDtos);
      ChatCompletionRequest chatCompletionRequest =
          createChatCompletionRequest(prompt, chatCompletionMessagesList, responseFormat);

      Object response = executeChatCompletion(prompt, chatCompletionRequest);
      log.debug("Inside @method executePrompt. response -> {}", response);
      return response;
    } catch (ResourceNotFoundException e) {
      log.error(PROMPT_NOT_FOUND, e.getMessage(), e);
      throw new BusinessException("Prompt not found: " + e.getMessage());
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error(PROMPT_NOT_FOUND, e.getMessage(), e);
      throw new BusinessException("Invalid prompt configuration: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error(PROMPT_NOT_FOUND, e.getMessage(), e);
      throw new BusinessException(e.getMessage());
    }
  }

  private ChatCompletionRequest createChatCompletionRequest(
      Prompt prompt,
      List<ChatCompletionMessage> chatCompletionMessagesList,
      ResponseFormat responseFormat) {
    return new ChatCompletionRequest(
        chatCompletionMessagesList,
        prompt.getLlmModel().getModel(),
        prompt.getTemperature(),
        prompt.getTopP(),
        prompt.getMaxToken(),
        responseFormat,
        prompt.getReasoningEffort());
  }

  private Object executeChatCompletion(Prompt prompt, ChatCompletionRequest chatCompletionRequest) {
    String inference = prompt.getLlmModel().getProvider();
    inference = inference == null ? "groq" : inference;
    OpenAiApi openAiApi = inferenceManager.getOpenAiApiByProvider(inference);
    ResponseEntity<ChatCompletion> response = openAiApi.chatCompletionEntity(chatCompletionRequest);
    ChatCompletionMessage message =
        Objects.requireNonNull(response.getBody()).choices().getFirst().message();
    log.debug("Inside @method executeChatCompletion. response -> {}", message.content());
    return message.content();
  }

  @Override
  public String executePromptV1(String promptId, Map<String, Object> variableMap) {
    log.debug("Inside @method executePromptV1 with promptId: {}", promptId);
    try {
      Prompt promptModel = promptRepository.getPromptByPromptId(promptId);
      refactorTemplateVariable(promptModel);
      List<org.springframework.ai.chat.messages.Message> messages =
          ChatModelUtils.getResolvedMessages(promptModel, variableMap);

      OpenAiChatModel openAiChatModel =
          OpenAiChatModel.builder()
              .openAiApi(
                  inferenceManager.getOpenAiApiByProvider(promptModel.getLlmModel().getProvider()))
              .build();

      ChatOptions chatOptions = ChatModelUtils.prepareChatOptions(promptModel);
      ChatResponse chatResponse =
          ChatClient.create(openAiChatModel)
              .prompt()
              .messages(messages)
              .options(chatOptions)
              .call()
              .chatResponse();

      return Objects.requireNonNull(chatResponse).getResult().getOutput().getText();
    } catch (GuardException e) {
        log.error("Security threat detected in user prompt : {}", e.getMessage(), e);
        throw new GuardException(e.getMessage(), e);
    } catch (ResourceNotFoundException e) {
      log.error(PROMPT_NOT_FOUND, e.getMessage(), e);
      throw new BusinessException("Prompt not found: " + e.getMessage());
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error(PROMPT_NOT_FOUND, e.getMessage(), e);
      throw new BusinessException("Invalid prompt configuration: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error(e.getMessage());
      throw new BusinessException(e.getMessage());
    }
  }

  @Override
  public String generateFreshSystemPrompt(String userInput, String type) {
    log.debug("inside @method generateFreshSystemPrompt. @param : userInput -> {}", userInput);
    try {

      ChatModel chatModel = inferenceManager.getChatModelByProvider("groq");

      String format = getFormat(type);
      String systemPromptString =
          TemplateUtils.getResolvedPrompt(freshSystemPrompt, Map.of(FORMAT, format));

      String userPromptString =
          TemplateUtils.getResolvedPrompt(freshUserPrompt, Map.of("userInput", userInput));
      ChatOptions chatOptions =
          OpenAiChatOptions.builder()
              .model(MODEL_LLAMA_3_3_70B_VERSATILE)
              .temperature(TEMPERATURE)
              .build();

      String content =
          ChatClient.create(chatModel)
              .prompt()
              .system(systemPromptString)
              .user(userPromptString)
              .options(chatOptions)
              .call()
              .content();

      return getOutput(type, content);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error while generating system prompt: {}", e.getMessage(), e);
      throw new BusinessException("Invalid input parameters: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error while generating system prompt: {}", e.getMessage(), e);
      throw new BusinessException("failed to generate prompt", e);
    }
  }

  private List<MessageDto> prepareMessageDtos(Prompt prompt, Map<String, Object> variableMap) {
    List<Message> messages = prompt.getMessages();
    List<MessageDto> messageDtos = new ArrayList<>();

    for (Message message : messages) {
      String promptString = message.getContent();

      if (!message.getRole().equals(SYSTEM)) {
        String updatedContent = promptString.replace("{{", "{").replace("}}", "}");
        replaceNullValues(variableMap);

        // If content becomes empty after replacement, skip it
        if (updatedContent.isEmpty()) {
          continue;
        }

        promptString = TemplateUtils.getResolvedPrompt(updatedContent, variableMap);
      }

      messageDtos.add(new MessageDto(message.getRole(), promptString));
    }

    return messageDtos;
  }

  private void applyDefaultJsonFormat(List<MessageDto> messageDtos) {
    MessageDto system =
        messageDtos.stream()
            .filter(message -> message.getRole().equalsIgnoreCase(SYSTEM))
            .findFirst()
            .orElse(null);
    if (system != null) {
      system.setContent(system.getContent() + DEFAULT_JSON_FORMAT);
    } else {
      messageDtos.add(new MessageDto(SYSTEM, DEFAULT_JSON_FORMAT));
    }
  }

  private ResponseFormat getResponseFormat(Prompt prompt) {
    if (Boolean.TRUE.equals(prompt.getJsonMode())) {
      return ResponseFormat.builder().type(ResponseFormat.Type.JSON_OBJECT).build();
    }
    return null;
  }

  private static String getFormat(String type) {
    String format;
    if (type.equals("CONVERSATION")) {
      BeanOutputConverter<ConversationPromptFormat> convertor =
          new BeanOutputConverter<>(ConversationPromptFormat.class);
      format = convertor.getFormat();
    } else {
      format = "Give output in plain string";
    }
    return format;
  }

  private static String getOutput(String type, String content) {
    String output;
    if (type.equals("CONVERSATION")) {
      BeanOutputConverter<ConversationPromptFormat> convertor =
          new BeanOutputConverter<>(ConversationPromptFormat.class);
      ConversationPromptFormat response = convertor.convert(content);
      if (response == null) {
        return content;
      }
      output =
          String.format(
              """
                            <role>%s</role>
                            <communicationStyle>%s</communicationStyle>
                            <responseFormat>%s</responseFormat>
                            <languageStyle>%s</languageStyle>
                            """,
              response.getRole(),
              response.getCommunicationStyle(),
              response.getResponseGuidelines(),
              response.getLanguageStyle());

    } else {
      output = content;
    }
    return output;
  }

  @Override
  public String improveSystemPrompt(String userInput, String oldPrompt, String type) {
    log.debug("inside @method improveSystemPrompt. @param : userInput -> {}", userInput);
    try {
      ChatModel chatModel = inferenceManager.getChatModelByProvider("groq");

      String format = getFormat(type);
      String systemPromptString =
          TemplateUtils.getResolvedPrompt(improveSystemPrompt, Map.of(FORMAT, format));

      Map<String, Object> variableMap =
          Map.of("oldSystemPrompt", oldPrompt, "userInstructions", userInput);
      String userPromptString = TemplateUtils.getResolvedPrompt(improveUserPrompt, variableMap);
      ChatOptions chatOptions =
          OpenAiChatOptions.builder()
              .model(MODEL_LLAMA_3_3_70B_VERSATILE)
              .temperature(TEMPERATURE)
              .build();

      String content =
          ChatClient.create(chatModel)
              .prompt()
              .system(systemPromptString)
              .user(userPromptString)
              .options(chatOptions)
              .call()
              .content();

      return getOutput(type, content);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error while improving system prompt: {}", e.getMessage(), e);
      throw new BusinessException("Invalid input parameters: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error while improving system prompt: {}", e.getMessage(), e);
      throw new BusinessException("failed to improve prompt", e);
    }
  }

  @Override
  public Map<String, Object> generateTestCase(String promptId, Map<String, String> request) {
    log.debug("inside @method generateTestCase");
    Prompt prompt =
        promptDao
            .findByNanoId(promptId)
            .orElseThrow(() -> new ResourceNotFoundException("prompt not found"));
    try {
      Message system =
          prompt.getMessages().stream()
              .filter(message -> message.getRole().equals(SYSTEM))
              .findFirst()
              .orElse(null);
      Message user =
          prompt.getMessages().stream()
              .filter(message -> message.getRole().equals("user"))
              .findFirst()
              .orElse(null);
      String context = request.get("context");
      String systemPrompt = system != null ? system.getContent() : "";
      String userPrompt = user != null ? user.getContent() : "";

      Set<String> promptVariables = getPromptVariables(systemPrompt, userPrompt);
      if (promptVariables.isEmpty()) {
        return Map.of(PromptConstants.RESULT, Map.of());
      }

      ChatModel chatModel = inferenceManager.getChatModelByProvider("groq");

      MapOutputConverter outputConverter = new MapOutputConverter();
      String format = outputConverter.getFormat();
      String systemPromptString =
          TemplateUtils.getResolvedPrompt(generateTestCaseSystem, Map.of(FORMAT, format));

      Map<String, Object> variableMap =
          Map.of(
              "systemPrompt",
              systemPrompt,
              "userPrompt",
              userPrompt,
              "promptVariables",
              promptVariables,
              "additionalContext",
              context);
      String userPromptString = TemplateUtils.getResolvedPrompt(generateTestCaseUser, variableMap);
      ChatOptions chatOptions =
          OpenAiChatOptions.builder()
              .model(MODEL_LLAMA_3_3_70B_VERSATILE)
              .temperature(TEMPERATURE)
              .build();

      String content =
          ChatClient.create(chatModel)
              .prompt()
              .messages(new SystemMessage(systemPromptString))
              .user(userPromptString)
              .options(chatOptions)
              .call()
              .content();

      List<Map<String, Object>> testCaseVariables =
          (List<Map<String, Object>>) (List<?>) JsonUtils.convertJsonToList(content, Map.class);
      sanitizeMap(testCaseVariables, promptVariables);
      return Map.of(PromptConstants.RESULT, testCaseVariables);
    } catch (ResourceNotFoundException e) {
      log.error(GENERATE_TEST_CASE_ERROR, e.getMessage(), e);
      throw new ResourceNotFoundException("Prompt not found.", e);
    } catch (JsonProcessingException e) {
      log.error(GENERATE_TEST_CASE_ERROR, e.getMessage(), e);
      throw new BusinessException("Failed to parse JSON response: " + e.getMessage());
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error(GENERATE_TEST_CASE_ERROR, e.getMessage(), e);
      throw new BusinessException("Invalid input parameters: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error(GENERATE_TEST_CASE_ERROR, e.getMessage(), e);
      throw new BusinessException("Failed to generate test case.", e);
    }
  }

  private void sanitizeMap(List<Map<String, Object>> maps, Set<String> keysToKeep) {
    log.debug("Inside @method sanitizeMap");
    try {
      for (Map<String, Object> map : maps) {
        retainKeys(map, keysToKeep);
      }
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method sanitizeMap : {}", e.getMessage(), e);
      throw new BusinessException("Invalid map operation: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method sanitizeMap : {}", e.getMessage(), e);
      throw new BusinessException("Failed to sanitize map.", e);
    }
  }

  private void retainKeys(Map<String, Object> map, Set<String> keysToKeep) {
    log.debug("Inside @method retainKeys");
    try {
      map.keySet().removeIf(key -> !keysToKeep.contains(key));
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method retainKeys : {}", e.getMessage(), e);
      throw new BusinessException("Invalid map operation: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method retainKeys : {}", e.getMessage(), e);
      throw new BusinessException("Failed to retain keys in map.", e);
    }
  }

  private Set<String> getPromptVariables(String systemPrompt, String userPrompt) {
    log.debug("Inside @method getPromptVariables");
    try {
      systemPrompt = refactorTemplateVariable(systemPrompt);
      userPrompt = refactorTemplateVariable(userPrompt);

      systemPrompt = !systemPrompt.isEmpty() ? systemPrompt : "---";
      userPrompt = !userPrompt.isEmpty() ? userPrompt : "---";

      CustomPromptTemplate systemTemplate = new CustomPromptTemplate(systemPrompt);
      Set<String> systemVariables = systemTemplate.getInputVariables();
      CustomPromptTemplate userTemplate = new CustomPromptTemplate(userPrompt);
      Set<String> userVariables = userTemplate.getInputVariables();
      Set<String> variables = new HashSet<>(systemVariables);
      variables.addAll(userVariables);
      return variables;
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method getPromptVariables : {}", e.getMessage(), e);
      throw new BusinessException("Invalid template format: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method getPromptVariables : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get prompt variables.", e);
    }
  }

  private String refactorTemplateVariable(String prompt) {
    log.debug("Inside @method refactorTemplateVariable");
    try {
      return prompt.replace("{{", "{").replace("}}", "}");
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method refactorTemplateVariable : {}", e.getMessage(), e);
      throw new BusinessException("Invalid template format: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method refactorTemplateVariable : {}", e.getMessage(), e);
      throw new BusinessException("Failed to refactor template variable.", e);
    }
  }

  private void refactorTemplateVariable(Prompt prompt) {
    log.debug("Inside @method refactorTemplateVariable (Prompt)");
    try {
      List<Message> messages = prompt.getMessages();

      for (Message message : messages) {
        if (!StringUtils.hasText(message.getContent())) {
          continue;
        }
        String updatedContent = message.getContent().replace("{{", "{").replace("}}", "}");
        message.setContent(updatedContent);
      }
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method refactorTemplateVariable (Prompt) : {}", e.getMessage(), e);
      throw new BusinessException("Invalid prompt template format: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method refactorTemplateVariable (Prompt) : {}", e.getMessage(), e);
      throw new BusinessException("Failed to refactor template variable in prompt.", e);
    }
  }

  private List<ChatCompletionMessage> getChatCompletionMessagesList(List<MessageDto> messages) {
    log.debug("Inside @method getChatCompletionMessagesList");
    try {
      Assert.notEmpty(messages, "messages can not be empty");
      log.debug("Converting MessageDto list to ChatCompletionMessage list");
      List<ChatCompletionMessage> chatCompletionMessages = new ArrayList<>();
      for (MessageDto messageDto : messages) {
        Role role = getRole(messageDto.getRole());
        ChatCompletionMessage chatCompletionMessage =
            getChatCompletionMessage(messageDto.getContent(), role);
        chatCompletionMessages.add(chatCompletionMessage);
      }
      return chatCompletionMessages;
    } catch (GuardException e) {
        log.error("Security threat detected in user prompt : {}", e.getMessage(), e);
        throw new GuardException(e.getMessage(), e);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method getChatCompletionMessagesList : {}", e.getMessage(), e);
      throw new BusinessException("Invalid message format: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method getChatCompletionMessagesList : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get chat completion messages list.", e);
    }
  }

  private OpenAiApi.ChatCompletionMessage getChatCompletionMessage(String template, Role role) {
    log.debug("Inside @method getChatCompletionMessage with @role: {}", role);
    if (role.equals(Role.USER)) {
        log.debug("Validating user prompt for security threats");
        GuardUtils.validateUserPrompt(template);
    }
    try {
      return new ChatCompletionMessage(template, role);
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method getChatCompletionMessage : {}", e.getMessage(), e);
      throw new BusinessException("Invalid message parameters: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method getChatCompletionMessage : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get chat completion message.", e);
    }
  }

  private Role getRole(String role) {
    log.debug("Inside @method getRole with @role: {}", role);
    try {
      return switch (role) {
        case "assistant" -> Role.ASSISTANT;
        case "user" -> Role.USER;
        case SYSTEM -> Role.SYSTEM;
        default -> throw new IllegalArgumentException("Invalid role: " + role);
      };
    } catch (IllegalArgumentException e) {
      log.error("Error in @method getRole : {}", e.getMessage(), e);
      throw new BusinessException("Invalid role: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method getRole : {}", e.getMessage(), e);
      throw new BusinessException("Failed to get role.", e);
    }
  }

  private void replaceNullValues(Map<String, Object> variableMap) {
    log.debug("Inside @method replaceNullValues");
    try {
      for (Map.Entry<String, Object> entry : variableMap.entrySet()) {
        if (entry.getValue() == null) {
          entry.setValue("");
        }
      }
    } catch (IllegalArgumentException | IllegalStateException e) {
      log.error("Error in @method replaceNullValues : {}", e.getMessage(), e);
      throw new BusinessException("Invalid map operation: " + e.getMessage());
    } catch (RuntimeException e) {
      log.error("Error in @method replaceNullValues : {}", e.getMessage(), e);
      throw new BusinessException("Failed to replace null values.", e);
    }
  }

  private static final String DEFAULT_JSON_FORMAT =
      """
            Your response should be in JSON format.
            Do not include any explanations, only provide a RFC8259 compliant JSON response following this format without deviation.
            Do not include markdown code blocks in your response.
            Remove the ```json markdown from the output.
            Here is the JSON Schema instance your output must adhere to:
            ```{
              "$schema" : "https://json-schema.org/draft/2020-12/schema",
              "type" : "object",
              "properties" : {
                "response" : {
                  "type" : "object",
                  "properties" : {
                    "answer" : {
                      "type" : "string"
                    }
                  },
                  "additionalProperties" : false
                },
                "status" : {
                  "type" : "string",
                  "description" : "The 'status' key can have only 'success' or 'failed' value"
                }
              },
              "additionalProperties" : false
            }```
            """;
}
